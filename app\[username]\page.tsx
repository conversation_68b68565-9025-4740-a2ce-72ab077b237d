"use client";

import { useEffect, use, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/lib/stores/user-store";
import { useThemeStore } from "@/lib/stores/theme-store";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cachedAPI } from "@/lib/cached-api";
import {
  ExternalLink,
  Instagram,
  Youtube,
  Twitter,
  Globe,
  Heart,
  Loader2
} from "lucide-react";



const getIconComponent = (iconType?: string) => {
  switch (iconType) {
    case "youtube":
      return <Youtube className="w-5 h-5" />;
    case "instagram":
      return <Instagram className="w-5 h-5" />;
    case "twitter":
      return <Twitter className="w-5 h-5" />;
    case "globe":
      return <Globe className="w-5 h-5" />;
    default:
      return <ExternalLink className="w-5 h-5" />;
  }
};

interface BioPageProps {
  params: Promise<{
    username: string;
  }>;
}

export default function BioPage({ params }: BioPageProps) {
  const { profile, links, socialLinks, incrementLinkClick, incrementPageView } = useUserStore();
  const { currentTheme } = useThemeStore();

  // Unwrap the params promise
  const { username } = use(params);

  // State for fetched data
  const [publicProfile, setPublicProfile] = useState<any>(null);
  const [publicLinks, setPublicLinks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if this is the user's own page
  const isOwnPage = profile?.username === username;

  // Get the data to display
  const userData = isOwnPage ? {
    name: profile.name,
    username: profile.username,
    bio: profile.bio,
    avatar: profile.avatar,
    template: profile.template,
    links: links.filter(link => link.isActive).sort((a, b) => a.order - b.order),
    socialLinks: socialLinks.filter(social => social.isVisible)
  } : publicProfile ? {
    name: publicProfile.name,
    username: publicProfile.username,
    bio: publicProfile.bio,
    avatar: publicProfile.avatar_url,
    template: publicProfile.template,
    links: publicLinks.map(link => ({
      id: link.id,
      title: link.title,
      url: link.url,
      description: link.description || '',
      icon: link.icon || '',
      backgroundColor: link.background_color || '',
      textColor: link.text_color || '',
      borderColor: link.border_color || '',
      customStyle: link.custom_style || '',
      clicks: link.clicks || 0,
      isActive: link.is_active,
      order: link.order
    })),
    socialLinks: [] // TODO: Add social links support
  } : null;

  console.log('👤 Final userData object:', userData);
  console.log('🖼️ Avatar in userData:', userData?.avatar);

  // Get the current design settings
  const getDesignSettings = () => {
    if (isOwnPage && profile) {
      // Use profile design settings
      return {
        background: {
          type: profile.backgroundType || 'color',
          value: profile.backgroundType === 'image' ? profile.backgroundImage : profile.backgroundColor,
          customColor: profile.backgroundColor
        },
        typography: {
          fontFamily: profile.fontFamily || 'font-sans',
          fontSize: 16,
          textColor: 'text-white'
        },
        linkCards: {
          style: currentTheme?.linkCards?.style || 'glass',
          size: currentTheme?.linkCards?.size || 60
        },
        effects: {
          glassmorphism: profile.glassmorphism || false
        }
      };
    } else if (publicProfile && publicProfile.design) {
      // Use public profile design settings
      const design = publicProfile.design;
      console.log('🎨 Applying public profile design:', design);
      return {
        background: {
          type: design.background?.type || 'color',
          value: design.background?.value || 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500',
          customColor: publicProfile.background_color
        },
        typography: {
          fontFamily: design.typography?.fontFamily || 'font-sans',
          fontSize: design.typography?.fontSize || 16,
          textColor: design.typography?.textColor || 'text-white',
          customTextColor: design.typography?.customTextColor
        },
        linkCards: {
          style: design.linkCards?.style || 'glass',
          size: design.linkCards?.size || 60
        },
        effects: {
          glassmorphism: publicProfile.glassmorphism || false
        }
      };
    }
    // Default design for non-own pages
    return {
      background: {
        type: 'color',
        value: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500'
      },
      typography: {
        fontFamily: 'font-sans',
        fontSize: 16,
        textColor: 'text-white'
      },
      linkCards: {
        style: 'glass',
        size: 60
      },
      effects: {
        glassmorphism: true
      }
    };
  };

  const design = getDesignSettings();

  // Helper function to get background styles
  const getBackgroundStyles = () => {
    if (design.background.type === 'image' && design.background.value) {
      return {
        backgroundImage: `url(${design.background.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    } else if (design.background.type === 'color' && design.background.customColor) {
      return {
        backgroundColor: design.background.customColor
      };
    }
    return {};
  };

  // Helper function to get link card styles based on selected style
  const getLinkCardStyles = (style: string) => {
    switch (style) {
      case 'glass':
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
      case 'neon':
        return 'bg-black border-2 border-cyan-400 shadow-lg shadow-cyan-400/50 hover:shadow-cyan-400/70';
      case 'minimal':
        return 'bg-white border border-gray-200 shadow-sm hover:shadow-md text-gray-900';
      case 'bold':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600';
      case 'soft':
        return 'bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200 hover:from-blue-100 hover:to-purple-100 text-gray-900';
      case 'dark':
        return 'bg-gray-900 border border-gray-700 text-white hover:bg-gray-800';
      default:
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
    }
  };

  // Fetch public profile data if not own page
  useEffect(() => {
    const fetchPublicData = async () => {
      if (isOwnPage) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch public profile (force refresh to get latest data)
        const profile = await cachedAPI.getPublicProfile(username, true);
        if (!profile) {
          setError('Profile not found');
          setIsLoading(false);
          return;
        }

        setPublicProfile(profile);
        console.log('✅ Public profile loaded:', profile);
        console.log('🖼️ Avatar URL from profile:', profile.avatar_url);

        // Fetch public links (force refresh to get latest data)
        const links = await cachedAPI.getPublicLinks(username, true);
        setPublicLinks(links || []);
        console.log('✅ Public links loaded:', links);

        setIsLoading(false);
      } catch (err) {
        console.error('Failed to fetch public data:', err);
        setError('Failed to load profile');
        setIsLoading(false);
      }
    };

    fetchPublicData();
  }, [username, isOwnPage]);

  useEffect(() => {
    // Track page view
    if (isOwnPage) {
      incrementPageView();
    }
  }, [isOwnPage, incrementPageView]);

  // Loading state
  if (isLoading) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={getBackgroundStyles()}
      >
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-white" />
          <p className="text-white">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={getBackgroundStyles()}
      >
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Profile Not Found
          </h1>
          <p className="text-white/80 mb-6">
            {error}
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  // No data state
  if (!userData) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={getBackgroundStyles()}
      >
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Profile Not Found
          </h1>
          <p className="text-white/80 mb-6">
            The profile you're looking for doesn't exist.
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  const handleLinkClick = (url: string, linkId?: string) => {
    // Track analytics if it's the user's own page
    if (isOwnPage && linkId) {
      incrementLinkClick(linkId);
    }
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      className={`min-h-screen ${design.background.type === 'color' && !design.background.customColor ? design.background.value : ''}`}
      style={getBackgroundStyles()}
    >
      <div className="max-w-md mx-auto px-4 py-8">
        {/* Profile Section */}
        <div className={`text-center mb-8 ${design.typography.fontFamily}`}>
          <Avatar className="w-24 h-24 mx-auto mb-4 ring-4 ring-white/30 shadow-lg">
            {userData.avatar && (
              <AvatarImage
                src={userData.avatar}
                onError={(e) => {
                  console.error('❌ Avatar failed to load:', userData.avatar);
                  console.error('Error details:', e);
                }}
                onLoad={() => {
                  console.log('✅ Avatar loaded successfully:', userData.avatar);
                }}
              />
            )}
            <AvatarFallback className="text-2xl bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              {userData.name.split(' ').map((n: string) => n[0]).join('')}
            </AvatarFallback>
          </Avatar>

          <h1
            className={`text-2xl font-bold mb-2`}
            style={{ color: design.typography.customTextColor || '#ffffff' }}
          >
            {userData.name}
          </h1>



          <p
            className={`mb-4 leading-relaxed opacity-90`}
            style={{ color: design.typography.customTextColor || '#ffffff' }}
          >
            {userData.bio}
          </p>

          {/* Social Links */}
          <div className="flex justify-center space-x-4 mb-6">
            {userData.socialLinks.map((social, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="rounded-full w-10 h-10 p-0 bg-white/20 border-white/30 hover:bg-white/30 text-white"
                onClick={() => handleLinkClick(social.url)}
              >
                {getIconComponent(social.platform)}
              </Button>
            ))}
          </div>
        </div>

        {/* Links Section */}
        <div className="space-y-4 mb-8">
          {userData.links && userData.links.length > 0 ? (
            userData.links.map((link) => (
              <Card
                key={link.id}
                className={`p-4 transition-all duration-300 cursor-pointer rounded-xl ${getLinkCardStyles(design.linkCards.style)} ${design.typography.fontFamily}`}
                onClick={() => handleLinkClick(link.url, link.id)}
              >
                <div className="relative flex items-center justify-center">
                  <div className="absolute left-3 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white">
                    {getIconComponent(link.icon)}
                  </div>
                  <div className="flex-1 text-center">
                    <h3 className={`font-semibold ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-900' : 'text-white'}`}>
                      {link.title}
                    </h3>
                    {link.description && (
                      <p className={`text-sm opacity-80 ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-600' : 'text-white'}`}>
                        {link.description}
                      </p>
                    )}
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8">
              <p
                className="opacity-70"
                style={{ color: design.typography.customTextColor || '#ffffff' }}
              >
                No links added yet
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className={`text-center ${design.typography.fontFamily}`}>
          <div className={`flex items-center justify-center space-x-2 text-sm mb-4 opacity-70 ${design.typography.textColor}`}>
            <Heart className="w-4 h-4" />
            <span>Made with</span>
            <Badge variant="secondary" className="bg-white/20 backdrop-blur-sm border-white/30 text-white">
              LinkVibe
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="text-xs bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Create your own bio page
          </Button>
        </div>
      </div>
    </div>
  );
}
