"use client";

import { useEffect, use, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/lib/stores/user-store";
import { useThemeStore } from "@/lib/stores/theme-store";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cachedAPI } from "@/lib/cached-api";
import {
  ExternalLink,
  Instagram,
  Youtube,
  Twitter,
  Globe,
  Heart,
  Loader2
} from "lucide-react";



const getIconComponent = (iconType?: string) => {
  switch (iconType) {
    case "youtube":
      return <Youtube className="w-5 h-5" />;
    case "instagram":
      return <Instagram className="w-5 h-5" />;
    case "twitter":
      return <Twitter className="w-5 h-5" />;
    case "globe":
      return <Globe className="w-5 h-5" />;
    default:
      return <ExternalLink className="w-5 h-5" />;
  }
};

interface BioPageProps {
  params: Promise<{
    username: string;
  }>;
}

export default function BioPage({ params }: BioPageProps) {
  const { profile, links, socialLinks, incrementLinkClick, incrementPageView } = useUserStore();
  const { currentTheme } = useThemeStore();

  // Unwrap the params promise
  const { username } = use(params);

  // State for fetched data
  const [publicProfile, setPublicProfile] = useState<any>(null);
  const [publicLinks, setPublicLinks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if this is the user's own page
  const isOwnPage = profile?.username === username;

  // Get the data to display (always use publicProfile data for consistency)
  const userData = publicProfile ? {
    name: publicProfile.name,
    username: publicProfile.username,
    bio: publicProfile.bio,
    avatar: publicProfile.avatar_url,
    template: publicProfile.template,
    links: publicLinks.map(link => ({
      id: link.id,
      title: link.title,
      url: link.url,
      description: link.description || '',
      icon: link.icon || '',
      backgroundColor: link.background_color || '',
      textColor: link.text_color || '',
      borderColor: link.border_color || '',
      customStyle: link.custom_style || '',
      clicks: link.clicks || 0,
      isActive: link.is_active,
      order: link.order
    })),
    socialLinks: [] // TODO: Add social links support
  } : null;





  // Get the current design settings
  const getDesignSettings = () => {
    if (isOwnPage && profile) {
      // Use profile design settings - check for new design field first, then fall back to individual fields
      if (publicProfile?.design) {
        const design = publicProfile.design;
        return {
          background: {
            type: design.background?.type || 'color',
            value: design.background?.value || 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500',
            customColor: design.background?.customColor
          },
          typography: {
            fontFamily: design.typography?.fontFamily || 'font-sans',
            fontSize: design.typography?.fontSize || 16,
            textColor: design.typography?.textColor || 'text-white',
            customTextColor: design.typography?.customTextColor
          },
          linkCards: {
            style: design.linkCards?.style || 'glass',
            size: design.linkCards?.size || 60
          },
          effects: {
            glassmorphism: design.effects?.glassmorphism || false
          }
        };
      } else {
        // Fall back to individual profile fields for backward compatibility
        return {
          background: {
            type: profile.backgroundType || 'color',
            value: profile.backgroundType === 'image' ? profile.backgroundImage : profile.backgroundColor,
            customColor: profile.backgroundColor
          },
          typography: {
            fontFamily: profile.fontFamily || 'font-sans',
            fontSize: 16,
            textColor: 'text-white'
          },
          linkCards: {
            style: currentTheme?.linkCards?.style || 'glass',
            size: currentTheme?.linkCards?.size || 60
          },
          effects: {
            glassmorphism: profile.glassmorphism || false
          }
        };
      }
    } else if (publicProfile && publicProfile.design) {
      // Use public profile design settings
      const design = publicProfile.design;

      return {
        background: {
          type: design.background?.type || 'color',
          value: design.background?.value || 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500',
          customColor: publicProfile.background_color
        },
        typography: {
          fontFamily: design.typography?.fontFamily || 'font-sans',
          fontSize: design.typography?.fontSize || 16,
          textColor: design.typography?.textColor || 'text-white',
          customTextColor: design.typography?.customTextColor
        },
        linkCards: {
          style: design.linkCards?.style || 'glass',
          size: design.linkCards?.size || 60
        },
        effects: {
          glassmorphism: publicProfile.glassmorphism || false
        }
      };
    }
    // Default design for non-own pages
    return {
      background: {
        type: 'color',
        value: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500'
      },
      typography: {
        fontFamily: 'font-sans',
        fontSize: 16,
        textColor: 'text-white'
      },
      linkCards: {
        style: 'glass',
        size: 60
      },
      effects: {
        glassmorphism: true
      }
    };
  };

  // Only get design settings if we have publicProfile data
  const design = publicProfile ? getDesignSettings() : {
    background: { type: 'color', value: 'bg-gray-100' },
    typography: { fontFamily: 'font-sans', fontSize: 16, textColor: 'text-gray-900' },
    linkCards: { style: 'minimal', size: 60 },
    effects: { glassmorphism: false }
  };

  // Helper function to get background style (similar to live-preview.tsx)
  const getBackgroundStyle = () => {
    // Only use image if backgroundType is specifically 'image' and we have an image URL
    if (design.background.type === 'image' && design.background.value) {
      const imageUrl = design.background.value.startsWith('url(')
        ? design.background.value
        : `url(${design.background.value})`;

      return {
        backgroundImage: imageUrl,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundColor: 'transparent'
      };
    }

    // Only use color if backgroundType is 'color' and we have a color value
    if (design.background.type === 'color' && design.background.value) {
      // Check if it's a hex color (solid color) or a Tailwind class (gradient)
      if (design.background.value.startsWith('#')) {
        return {
          backgroundColor: design.background.value,
          backgroundImage: 'none'
        };
      } else {
        return design.background.value; // Return Tailwind gradient class as string
      }
    }

    if (design.background.type === 'none') {
      return 'bg-transparent';
    }

    return 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
  };

  // Helper functions for text color (similar to live-preview.tsx)
  const getTextColor = () => {
    const textColor = design.typography?.customTextColor || design.typography?.textColor || 'text-white';
    // If it's a custom color (starts with #), return empty string and use inline style
    if (textColor.startsWith('#')) {
      return '';
    }
    return textColor;
  };

  const getTextStyle = () => {
    const textColor = design.typography?.customTextColor || design.typography?.textColor || 'text-white';
    // If it's a custom color (hex), return as inline style
    if (textColor.startsWith('#')) {
      return { color: textColor };
    }
    return {};
  };

  // Helper functions for font (similar to live-preview.tsx)
  const getFontClass = () => {
    const fontFamily = design.typography?.fontFamily;
    if (fontFamily && (fontFamily.includes(',') || fontFamily.includes('"') || fontFamily.includes("'"))) {
      return '';
    }
    return fontFamily || 'font-sans';
  };

  const getFontStyle = () => {
    const fontFamily = design.typography?.fontFamily;
    if (fontFamily && (fontFamily.includes(',') || fontFamily.includes('"') || fontFamily.includes("'"))) {
      return { fontFamily };
    }
    return {};
  };

  // Helper function to get link card styles based on selected style
  const getLinkCardStyles = (style: string) => {
    switch (style) {
      case 'glass':
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
      case 'neon':
        return 'bg-black border-2 border-cyan-400 shadow-lg shadow-cyan-400/50 hover:shadow-cyan-400/70';
      case 'minimal':
        return 'bg-white border border-gray-200 shadow-sm hover:shadow-md text-gray-900';
      case 'bold':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600';
      case 'soft':
        return 'bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200 hover:from-blue-100 hover:to-purple-100 text-gray-900';
      case 'dark':
        return 'bg-gray-900 border border-gray-700 text-white hover:bg-gray-800';
      default:
        return 'bg-white/20 backdrop-blur-lg border border-white/30 hover:bg-white/30';
    }
  };

  // Fetch public profile data (always fetch from Supabase for latest data)
  useEffect(() => {
    const fetchPublicData = async () => {
      // Always fetch from Supabase to get the latest data

      try {
        setIsLoading(true);
        setError(null);

        // Import userService directly to bypass cache issues
        const { userService } = await import('@/lib/user-service');

        // Fetch directly from userService to bypass cache
        const profile = await userService.getUserProfileByUsername(username);
        if (!profile) {
          setError('Profile not found');
          setIsLoading(false);
          return;
        }

        setPublicProfile(profile);


        // Fetch public links (force refresh to get latest data from Supabase)
        const links = await cachedAPI.getPublicLinks(username, true);
        setPublicLinks(links || []);

        setIsLoading(false);
      } catch (err) {
        console.error('Failed to fetch public data:', err);
        setError('Failed to load profile');
        setIsLoading(false);
      }
    };

    fetchPublicData();
  }, [username, isOwnPage]);

  useEffect(() => {
    // Track page view
    if (isOwnPage) {
      incrementPageView();
    }
  }, [isOwnPage, incrementPageView]);

  // Loading state
  if (isLoading) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${typeof getBackgroundStyle() === 'string' ? getBackgroundStyle() : ''}`}
        style={typeof getBackgroundStyle() === 'object' ? getBackgroundStyle() as React.CSSProperties : {}}
      >
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-white" />
          <p className="text-white">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${typeof getBackgroundStyle() === 'string' ? getBackgroundStyle() : ''}`}
        style={typeof getBackgroundStyle() === 'object' ? getBackgroundStyle() as React.CSSProperties : {}}
      >
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Profile Not Found
          </h1>
          <p className="text-white/80 mb-6">
            {error}
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  // No data state
  if (!userData) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${typeof getBackgroundStyle() === 'string' ? getBackgroundStyle() : ''}`}
        style={typeof getBackgroundStyle() === 'object' ? getBackgroundStyle() as React.CSSProperties : {}}
      >
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Profile Not Found
          </h1>
          <p className="text-white/80 mb-6">
            The profile you're looking for doesn't exist.
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  const handleLinkClick = (url: string, linkId?: string) => {
    // Track analytics if it's the user's own page
    if (isOwnPage && linkId) {
      incrementLinkClick(linkId);
    }
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      className={`min-h-screen ${typeof getBackgroundStyle() === 'string' ? getBackgroundStyle() : ''}`}
      style={typeof getBackgroundStyle() === 'object' ? getBackgroundStyle() as React.CSSProperties : {}}
    >
      <div className="max-w-md mx-auto px-4 py-8">
        {/* Profile Section */}
        <div className={`text-center mb-8 ${getFontClass()}`} style={getFontStyle()}>
          <Avatar className="w-24 h-24 mx-auto mb-4 ring-4 ring-white/30 shadow-lg">
            {userData.avatar && (
              <AvatarImage
                src={userData.avatar}
                onError={(e) => {
                  console.error('❌ Avatar failed to load:', userData.avatar);
                  console.error('Error details:', e);
                }}
                onLoad={() => {
                  console.log('✅ Avatar loaded successfully:', userData.avatar);
                }}
              />
            )}
            <AvatarFallback className="text-2xl bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              {userData.name.split(' ').map((n: string) => n[0]).join('')}
            </AvatarFallback>
          </Avatar>

          <h1
            className={`text-2xl font-bold mb-2 ${getFontClass()} ${getTextColor()}`}
            style={{...getFontStyle(), ...getTextStyle()}}
          >
            {userData.name}
          </h1>






          <p
            className={`mb-4 leading-relaxed opacity-90 ${getFontClass()} ${getTextColor()}`}
            style={{...getFontStyle(), ...getTextStyle()}}
          >
            {userData.bio}
          </p>

          {/* Social Links - TODO: Implement social links from Supabase */}
        </div>

        {/* Links Section */}
        <div className="space-y-4 mb-8">
          {userData.links && userData.links.length > 0 ? (
            userData.links.map((link) => (
              <Card
                key={link.id}
                className={`p-4 transition-all duration-300 cursor-pointer rounded-xl ${getLinkCardStyles(design.linkCards.style)} ${design.typography.fontFamily}`}
                onClick={() => handleLinkClick(link.url, link.id)}
              >
                <div className="relative flex items-center justify-center">
                  <div className={`absolute left-3 w-10 h-10 rounded-lg flex items-center justify-center ${
                    link.backgroundColor || 'bg-gradient-to-br from-purple-500 to-pink-500'
                  } ${link.textColor || 'text-white'}`}>
                    {getIconComponent(link.icon)}
                  </div>
                  <div className="flex-1 text-center">
                    <h3
                      className={`font-semibold ${getFontClass()} ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-900' : 'text-white'}`}
                      style={getFontStyle()}
                    >
                      {link.title}
                    </h3>
                    {link.description && (
                      <p
                        className={`text-sm opacity-80 ${getFontClass()} ${design.linkCards.style === 'minimal' || design.linkCards.style === 'soft' ? 'text-gray-600' : 'text-white'}`}
                        style={getFontStyle()}
                      >
                        {link.description}
                      </p>
                    )}
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8">
              <p
                className={`opacity-70 ${getTextColor()}`}
                style={getTextStyle()}
              >
                No links added yet
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className={`text-center ${getFontClass()}`} style={getFontStyle()}>
          <div className={`flex items-center justify-center space-x-2 text-sm mb-4 opacity-70 ${design.typography.textColor}`}>
            <Heart className="w-4 h-4" />
            <span>Made with</span>
            <Badge variant="secondary" className="bg-white/20 backdrop-blur-sm border-white/30 text-white">
              LinkVibe
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/', '_blank')}
            className="text-xs bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            Create your own bio page
          </Button>
        </div>
      </div>
    </div>
  );
}
