'use client';

import React from 'react';

interface CinematicEffectsProps {
  enabled?: boolean;
  intensity?: 'low' | 'medium' | 'high';
  effect?: 'none' | 'motion-blur' | 'faded-sides' | 'aesthetic-vibe' | 'cinematic' | 'vintage' | 'dreamy';
  children: React.ReactNode;
}

export default function CinematicEffects({ 
  enabled = true, 
  intensity = 'medium',
  effect = 'none',
  children 
}: CinematicEffectsProps) {
  if (!enabled || effect === 'none') {
    return <>{children}</>;
  }

  const getEffectStyles = () => {
    const baseIntensity = {
      low: { opacity: 0.3, blur: '0.5px' },
      medium: { opacity: 0.5, blur: '1px' },
      high: { opacity: 0.7, blur: '1.5px' }
    }[intensity];

    switch (effect) {
      case 'motion-blur':
        return {
          filter: `blur(${baseIntensity.blur}) brightness(1.1)`,
          background: `
            linear-gradient(
              90deg,
              rgba(0,0,0,0.1) 0%,
              transparent 20%,
              transparent 80%,
              rgba(0,0,0,0.1) 100%
            )
          `,
          animation: 'motionBlur 3s ease-in-out infinite alternate'
        };

      case 'faded-sides':
        return {
          background: `
            radial-gradient(
              ellipse at center,
              transparent 30%,
              rgba(0,0,0,${baseIntensity.opacity * 0.6}) 100%
            )
          `,
          filter: 'brightness(1.05) contrast(1.1)'
        };

      case 'aesthetic-vibe':
        return {
          filter: `saturate(1.3) contrast(1.2) brightness(1.1) hue-rotate(5deg)`,
          background: `
            linear-gradient(
              135deg,
              rgba(255,182,193,${baseIntensity.opacity * 0.2}) 0%,
              rgba(255,218,185,${baseIntensity.opacity * 0.1}) 25%,
              rgba(186,225,255,${baseIntensity.opacity * 0.1}) 75%,
              rgba(221,160,221,${baseIntensity.opacity * 0.2}) 100%
            )
          `,
          mixBlendMode: 'soft-light' as const
        };

      case 'cinematic':
        return {
          filter: `contrast(1.3) brightness(0.9) saturate(1.1)`,
          background: `
            linear-gradient(
              135deg,
              rgba(0,0,0,${baseIntensity.opacity * 0.4}) 0%,
              rgba(0,0,0,${baseIntensity.opacity * 0.1}) 30%,
              rgba(0,0,0,${baseIntensity.opacity * 0.2}) 70%,
              rgba(0,0,0,${baseIntensity.opacity * 0.6}) 100%
            )
          `,
          boxShadow: 'inset 0 0 100px rgba(0,0,0,0.3)'
        };

      case 'vintage':
        return {
          filter: `sepia(0.3) contrast(1.2) brightness(0.95) saturate(0.8)`,
          background: `
            radial-gradient(
              circle at center,
              rgba(139,69,19,${baseIntensity.opacity * 0.1}) 0%,
              rgba(160,82,45,${baseIntensity.opacity * 0.2}) 50%,
              rgba(101,67,33,${baseIntensity.opacity * 0.4}) 100%
            )
          `,
          mixBlendMode: 'multiply' as const
        };

      case 'dreamy':
        return {
          filter: `blur(0.5px) brightness(1.2) saturate(1.1)`,
          background: `
            radial-gradient(
              circle at 30% 70%,
              rgba(255,182,193,${baseIntensity.opacity * 0.3}) 0%,
              transparent 50%
            ),
            radial-gradient(
              circle at 70% 30%,
              rgba(173,216,230,${baseIntensity.opacity * 0.3}) 0%,
              transparent 50%
            ),
            linear-gradient(
              45deg,
              rgba(255,255,255,${baseIntensity.opacity * 0.1}) 0%,
              transparent 100%
            )
          `,
          animation: 'dreamyFloat 6s ease-in-out infinite alternate'
        };

      default:
        return {};
    }
  };

  const effectStyles = getEffectStyles();

  return (
    <div className="relative overflow-hidden">
      {/* Main content */}
      <div 
        style={{
          filter: effectStyles.filter,
          transition: 'filter 0.3s ease'
        }}
      >
        {children}
      </div>

      {/* Effect overlay */}
      <div 
        className="absolute inset-0 pointer-events-none"
        style={{
          background: effectStyles.background,
          mixBlendMode: effectStyles.mixBlendMode,
          boxShadow: effectStyles.boxShadow,
          animation: effectStyles.animation
        }}
      />

      {/* Additional particle effects for certain styles */}
      {(effect === 'aesthetic-vibe' || effect === 'dreamy') && (
        <div 
          className="absolute inset-0 pointer-events-none opacity-20"
          style={{
            backgroundImage: `
              radial-gradient(circle at 20% 80%, rgba(255,182,193,0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(173,216,230,0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255,218,185,0.3) 0%, transparent 50%)
            `,
            animation: 'particleFloat 8s ease-in-out infinite alternate'
          }}
        />
      )}

      <style jsx>{`
        @keyframes motionBlur {
          0% { 
            transform: translateX(-2px) skewX(-1deg); 
          }
          100% { 
            transform: translateX(2px) skewX(1deg); 
          }
        }

        @keyframes dreamyFloat {
          0% { 
            transform: translateY(-2px) scale(1); 
            opacity: 0.3;
          }
          100% { 
            transform: translateY(2px) scale(1.01); 
            opacity: 0.5;
          }
        }

        @keyframes particleFloat {
          0% { 
            transform: translate(0, 0) scale(1); 
            opacity: 0.1;
          }
          50% { 
            transform: translate(-1px, 1px) scale(1.01); 
            opacity: 0.2;
          }
          100% { 
            transform: translate(1px, -1px) scale(0.99); 
            opacity: 0.1;
          }
        }
      `}</style>
    </div>
  );
}
