"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  ExternalLink,
  Eye,
  Heart,
  Youtube,
  Instagram,
  Twitter,
  Facebook,
  Linkedin,
  Github,
  Globe,
  Mail,
  Phone,
  MapPin,
  ShoppingBag,
  Camera,
  Music,
  Video,
  BookOpen,
  Coffee,
  Star,
  Zap,
  Rocket,
  Palette,
  Code,
  Briefcase,
  GraduationCap,
  Home,
  User,
  Settings,
  Search,
  Plus,
  Download,
  Upload,
  Edit,
  Trash,
  Lock,
  Unlock,
  Bell,
  Calendar,
  Clock,
  Flag,
  Gift,
  Headphones,
  Image,
  Link,
  MessageCircle,
  Mic,
  Monitor,
  Smartphone,
  Tablet,
  Wifi,
  Bluetooth,
  Battery,
  Volume2,
  Play,
  Pause,
  SkipFor<PERSON>,
  Ski<PERSON><PERSON><PERSON>,
  Repeat,
  Shuffle
} from "lucide-react";
import CinematicEffects from './cinematic-effects';

interface LivePreviewProps {
  profile: {
    name: string;
    bio: string;
    avatar: string;
    username: string;
  };
  links: Array<{
    id: string;
    title: string;
    url: string;
    description?: string;
    icon: string;
    clicks: number;
    isActive: boolean;
    backgroundColor?: string;
  }>;
  design: {
    background?: {
      type: 'color' | 'image';
      value: string;
      effect?: 'none' | 'motion-blur' | 'faded-sides' | 'aesthetic-vibe' | 'cinematic' | 'vintage' | 'dreamy';
    };
    typography?: {
      fontFamily: string;
      fontSize: number;
      textColor?: string;
      customTextColor?: string;
    };
    linkCards?: {
      style: string;
      size: number;
    };
  };
  className?: string;
}

// Icon mapping function
const getIconComponent = (iconType: string) => {
  switch (iconType) {
    case "youtube":
      return <Youtube className="w-4 h-4" />;
    case "instagram":
      return <Instagram className="w-4 h-4" />;
    case "twitter":
      return <Twitter className="w-4 h-4" />;
    case "facebook":
      return <Facebook className="w-4 h-4" />;
    case "linkedin":
      return <Linkedin className="w-4 h-4" />;
    case "github":
      return <Github className="w-4 h-4" />;
    case "globe":
      return <Globe className="w-4 h-4" />;
    case "mail":
      return <Mail className="w-4 h-4" />;
    case "phone":
      return <Phone className="w-4 h-4" />;
    case "map-pin":
      return <MapPin className="w-4 h-4" />;
    case "shopping-bag":
      return <ShoppingBag className="w-4 h-4" />;
    case "camera":
      return <Camera className="w-4 h-4" />;
    case "music":
      return <Music className="w-4 h-4" />;
    case "video":
      return <Video className="w-4 h-4" />;
    case "book-open":
      return <BookOpen className="w-4 h-4" />;
    case "coffee":
      return <Coffee className="w-4 h-4" />;
    case "heart":
      return <Heart className="w-4 h-4" />;
    case "star":
      return <Star className="w-4 h-4" />;
    case "zap":
      return <Zap className="w-4 h-4" />;
    case "rocket":
      return <Rocket className="w-4 h-4" />;
    case "palette":
      return <Palette className="w-4 h-4" />;
    case "code":
      return <Code className="w-4 h-4" />;
    case "briefcase":
      return <Briefcase className="w-4 h-4" />;
    case "graduation-cap":
      return <GraduationCap className="w-4 h-4" />;
    case "home":
      return <Home className="w-4 h-4" />;
    case "user":
      return <User className="w-4 h-4" />;
    case "settings":
      return <Settings className="w-4 h-4" />;
    case "search":
      return <Search className="w-4 h-4" />;
    case "plus":
      return <Plus className="w-4 h-4" />;
    case "download":
      return <Download className="w-4 h-4" />;
    case "upload":
      return <Upload className="w-4 h-4" />;
    case "edit":
      return <Edit className="w-4 h-4" />;
    case "trash":
      return <Trash className="w-4 h-4" />;
    case "lock":
      return <Lock className="w-4 h-4" />;
    case "unlock":
      return <Unlock className="w-4 h-4" />;
    case "bell":
      return <Bell className="w-4 h-4" />;
    case "calendar":
      return <Calendar className="w-4 h-4" />;
    case "clock":
      return <Clock className="w-4 h-4" />;
    case "flag":
      return <Flag className="w-4 h-4" />;
    case "gift":
      return <Gift className="w-4 h-4" />;
    case "headphones":
      return <Headphones className="w-4 h-4" />;
    case "image":
      return <Image className="w-4 h-4" />;
    case "link":
      return <Link className="w-4 h-4" />;
    case "message-circle":
      return <MessageCircle className="w-4 h-4" />;
    case "mic":
      return <Mic className="w-4 h-4" />;
    case "monitor":
      return <Monitor className="w-4 h-4" />;
    case "smartphone":
      return <Smartphone className="w-4 h-4" />;
    case "tablet":
      return <Tablet className="w-4 h-4" />;
    case "wifi":
      return <Wifi className="w-4 h-4" />;
    case "bluetooth":
      return <Bluetooth className="w-4 h-4" />;
    case "battery":
      return <Battery className="w-4 h-4" />;
    case "volume-2":
      return <Volume2 className="w-4 h-4" />;
    case "play":
      return <Play className="w-4 h-4" />;
    case "pause":
      return <Pause className="w-4 h-4" />;
    case "skip-forward":
      return <SkipForward className="w-4 h-4" />;
    case "skip-back":
      return <SkipBack className="w-4 h-4" />;
    case "repeat":
      return <Repeat className="w-4 h-4" />;
    case "shuffle":
      return <Shuffle className="w-4 h-4" />;
    default:
      return <ExternalLink className="w-4 h-4" />;
  }
};

export function LivePreview({ profile, links, design, className = "" }: LivePreviewProps) {
  // Add a state to force re-render when design changes
  const [renderKey, setRenderKey] = useState(Date.now());

  // Force re-render when design changes, especially for background images
  useEffect(() => {
    setRenderKey(Date.now());
  }, [design, design?.background?.type, design?.background?.value]);

  const getBackgroundStyle = () => {
    // Only use image if backgroundType is specifically 'image' and we have an image URL
    if (design.background?.type === 'image' && design.background.value) {
      // Check if the value already has url() wrapper
      const imageUrl = design.background.value.startsWith('url(')
        ? design.background.value
        : `url(${design.background.value})`;

      return {
        backgroundImage: imageUrl,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        // Explicitly clear backgroundColor to prevent conflicts
        backgroundColor: 'transparent'
      };
    }

    // Only use color if backgroundType is 'color' and we have a color value
    if (design.background?.type === 'color' && design.background.value) {
      // Check if it's a hex color (solid color) or a Tailwind class (gradient)
      if (design.background.value.startsWith('#')) {
        return {
          backgroundColor: design.background.value,
          // Explicitly clear backgroundImage to prevent conflicts
          backgroundImage: 'none'
        };
      } else {
        return design.background.value; // Tailwind gradient class
      }
    }

    if (design.background?.type === 'none') {
      return 'bg-transparent';
    }

    return 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500';
  };

  const getFontClass = () => {
    // If fontFamily is a CSS font-family string, return empty string and use inline style
    const fontFamily = design.typography?.fontFamily;
    if (fontFamily && (fontFamily.includes(',') || fontFamily.includes('"') || fontFamily.includes("'"))) {
      return '';
    }
    return fontFamily || 'font-sans';
  };

  const getFontStyle = () => {
    const fontFamily = design.typography?.fontFamily;
    // If fontFamily is a CSS font-family string, return it as inline style
    if (fontFamily && (fontFamily.includes(',') || fontFamily.includes('"') || fontFamily.includes("'"))) {
      return { fontFamily };
    }
    return {};
  };

  const getTextColor = () => {
    const textColor = design.typography?.textColor || 'text-white';
    // If it's a custom color (starts with [color:), return empty string and use inline style
    if (textColor.startsWith('[color:')) {
      return '';
    }
    return textColor;
  };

  const getTextStyle = () => {
    const textColor = design.typography?.textColor || 'text-white';
    // If it's a custom color, extract the color value and return as inline style
    if (textColor.startsWith('[color:') && textColor.endsWith(']')) {
      const color = textColor.slice(7, -1); // Remove [color: and ]
      return { color };
    }
    return {};
  };

  const getLinkCardStyle = (linkStyle: string) => {
    const styles = {
      glass: "bg-white/20 backdrop-blur-lg border border-white/30",
      neon: "bg-black border-2 border-cyan-400 shadow-lg shadow-cyan-400/50",
      minimal: "bg-white border border-gray-200 shadow-sm",
      bold: "bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0",
      soft: "bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-200",
      dark: "bg-gray-900 border border-gray-700 text-white"
    };
    return styles[linkStyle as keyof typeof styles] || styles.glass;
  };

  const getLinkTextColor = (linkStyle: string) => {
    // First check if there's a custom text color set
    const customTextColor = design.typography?.textColor;
    if (customTextColor && customTextColor.startsWith('[color:')) {
      return ''; // Return empty string to use inline style
    }
    if (customTextColor && customTextColor !== 'text-white') {
      return customTextColor;
    }

    // Fallback to style-based colors
    const darkStyles = ['neon', 'bold', 'dark'];
    if (darkStyles.includes(linkStyle)) {
      return 'text-white';
    }
    if (linkStyle === 'minimal' || linkStyle === 'soft') {
      return 'text-gray-900';
    }
    return getTextColor();
  };

  const getLinkTextStyle = (linkStyle: string) => {
    // Check if there's a custom text color set
    const customTextColor = design.typography?.textColor;
    if (customTextColor && customTextColor.startsWith('[color:') && customTextColor.endsWith(']')) {
      const color = customTextColor.slice(7, -1); // Remove [color: and ]
      return { color };
    }
    return {};
  };

  const handleLinkClick = (url: string, linkId: string) => {
    // Simulate click tracking
    console.log(`Clicked link: ${linkId} - ${url}`);
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div>
            <CardTitle className="flex items-center">
              <Eye className="w-5 h-5 mr-2" />
              Live Preview
            </CardTitle>
            <CardDescription className="text-blue-100">
              Real-time preview of your bio page
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="p-6">


          {/* Preview Container */}
          <div className="flex justify-center">
            <div className="w-full max-w-sm border-4 border-gray-300 rounded-2xl overflow-hidden shadow-xl bg-white relative" style={{ height: '600px' }}>
              {/* Phone/Device Frame */}
              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gray-400 rounded-full" />
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 border-2 border-gray-400 rounded-full" />

              {/* Bio Page Content */}
              <div
                key={renderKey}
                className={`h-full overflow-y-auto ${getFontClass()} relative`}
                style={{
                  ...getFontStyle()
                }}
              >
                {/* Background Layer */}
                <div className="absolute inset-0">
                  {typeof getBackgroundStyle() === 'string' && (
                    <div className={`absolute inset-0 ${getBackgroundStyle()}`} />
                  )}
                  {typeof getBackgroundStyle() === 'object' && (
                    <div
                      className="absolute inset-0"
                      style={getBackgroundStyle() as React.CSSProperties}
                    />
                  )}
                </div>

                {/* Effects Layer - Only for image backgrounds */}
                {design.background?.type === 'image' && design.background?.effect && design.background?.effect !== 'none' && (
                  <CinematicEffects
                    enabled={true}
                    intensity="medium"
                    effect={design.background?.effect}
                  >
                    <div className="absolute inset-0" />
                  </CinematicEffects>
                )}

                {/* Content Layer - Always on top */}
                <div className="relative z-10 h-full">
                  <div className="p-6 text-center space-y-4">
                  {/* Profile Section */}
                  <div className="space-y-3">
                    <Avatar className="w-20 h-20 mx-auto border-4 border-white/50 shadow-lg">
                      {profile.avatar && <AvatarImage src={profile.avatar} />}
                      <AvatarFallback className="text-xl bg-white/20 text-white">
                        {profile.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="space-y-1 px-2">
                      <h1 className={`text-lg font-bold ${getTextColor()} drop-shadow-lg`} style={getTextStyle()}>
                        {profile.name}
                      </h1>
                      <p className={`${getTextColor()} opacity-90 text-xs drop-shadow leading-tight`} style={getTextStyle()}>
                        {profile.bio}
                      </p>
                    </div>

                    {/* Username Badge */}
                    <Badge className={`bg-white/20 ${getTextColor()} border-white/30 backdrop-blur-sm`} style={getTextStyle()}>
                      @{profile.username}
                    </Badge>
                  </div>

                  {/* Links Section */}
                  <div className="space-y-3 pt-2">
                    {links.filter(link => {
                      console.log('🔍 Link:', link.title, 'isActive:', link.isActive, 'type:', typeof link.isActive);
                      return link.isActive;
                    }).map((link) => {
                      const linkStyle = design.linkCards?.style || 'glass';
                      const linkCardClasses = getLinkCardStyle(linkStyle);
                      const linkTextColor = getLinkTextColor(linkStyle);

                      return (
                        <div
                          key={link.id}
                          className={`${linkCardClasses} rounded-lg p-3 cursor-pointer hover:opacity-80 transition-all duration-300 transform hover:scale-105 mx-2`}
                          onClick={() => handleLinkClick(link.url, link.id)}
                        >
                          <div className="relative flex items-center justify-center">
                            <div className={`absolute left-2 w-8 h-8 rounded-lg flex items-center justify-center ${link.backgroundColor && link.backgroundColor.trim() !== '' ? link.backgroundColor : ''}`}>
                              <span className={linkTextColor} style={getLinkTextStyle(linkStyle)}>
                                {getIconComponent(link.icon)}
                              </span>
                            </div>
                            <div className="flex-1 text-center">
                              <h3 className={`font-semibold ${linkTextColor} text-xs`} style={getLinkTextStyle(linkStyle)}>{link.title}</h3>
                              {link.description && (
                                <p className={`${linkTextColor} opacity-80 text-xs`} style={getLinkTextStyle(linkStyle)}>{link.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Footer */}
                  <div className="pt-4 space-y-2">
                    <div className={`flex items-center justify-center space-x-2 ${getTextColor()} opacity-70 text-xs`} style={getTextStyle()}>
                      <Heart className="w-3 h-3" />
                      <span>Made with LinkVibe</span>
                    </div>
                  </div>
                </div>
                </div>
              </div>
            </div>
          </div>

          {/* Preview URL */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ExternalLink className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600 font-mono">
                  linkvibe.app/{profile.username}
                </span>
              </div>
              <Button size="sm" variant="outline" className="text-xs">
                Copy Link
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
